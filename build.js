#!/usr/bin/env node

/**
 * GamePort 静态网站构建脚本
 * 生成主页和游戏页面，使用相同的模板结构
 */

const fs = require('fs');
const path = require('path');

// 配置
const CONFIG = {
    srcDir: './src',
    distDir: './dist',
    templatesDir: './src/templates',
    componentsDir: './src/components',
    gamesDataFile: './games.json'
};

// 确保输出目录存在
function ensureDirectoryExists(dirPath) {
    if (!fs.existsSync(dirPath)) {
        fs.mkdirSync(dirPath, { recursive: true });
        console.log(`📁 Created directory: ${dirPath}`);
    }
}

// 读取文件内容
function readFile(filePath) {
    try {
        return fs.readFileSync(filePath, 'utf8');
    } catch (error) {
        console.error(`❌ Error reading file ${filePath}:`, error.message);
        return '';
    }
}

// 写入文件
function writeFile(filePath, content) {
    try {
        fs.writeFileSync(filePath, content, 'utf8');
        console.log(`✅ Generated: ${filePath}`);
    } catch (error) {
        console.error(`❌ Error writing file ${filePath}:`, error.message);
    }
}

// 复制目录
function copyDirectory(src, dest) {
    ensureDirectoryExists(dest);
    const items = fs.readdirSync(src);
    
    items.forEach(item => {
        const srcPath = path.join(src, item);
        const destPath = path.join(dest, item);
        
        if (fs.statSync(srcPath).isDirectory()) {
            copyDirectory(srcPath, destPath);
        } else {
            fs.copyFileSync(srcPath, destPath);
        }
    });
}

// 读取游戏数据
function loadGamesData() {
    try {
        const data = fs.readFileSync(CONFIG.gamesDataFile, 'utf8');
        const gamesArray = JSON.parse(data);

        // 根据PRD 2.4节要求：
        // 数组第一个对象[0]专门用于生成index.html的核心内容
        // 数组从第二个对象[1]开始为列表游戏
        return {
            homepageGame: gamesArray[0] || null,
            listGames: gamesArray.slice(1) || []
        };
    } catch (error) {
        console.error(`❌ Error loading games data:`, error.message);
        return {
            homepageGame: null,
            listGames: []
        };
    }
}

// 读取配置数据
function loadConfig() {
    try {
        const data = fs.readFileSync('./config.json', 'utf8');
        return JSON.parse(data);
    } catch (error) {
        console.error(`❌ Error loading config:`, error.message);
        return {
            site_name: 'GamePort',
            selected_theme: 'default',
            site_url: 'https://gameport.example.com'
        };
    }
}

// 生成游戏卡片HTML
function generateGameCard(game) {
    const cardTemplate = readFile(path.join(CONFIG.componentsDir, 'game-card.html'));

    return cardTemplate
        .replace(/\{\{GAME_ID\}\}/g, game.id)
        .replace(/\{\{GAME_NAME\}\}/g, game.name)
        .replace(/\{\{GAME_THUMBNAIL\}\}/g, game.thumbnail)
        .replace(/\{\{GAME_CATEGORY\}\}/g, game.category);
}

// 生成游戏列表HTML
function generateGamesList(games, category = null) {
    let filteredGames = games;

    if (category) {
        filteredGames = games.filter(game => game.category === category);
    }

    return filteredGames.map(game => generateGameCard(game)).join('\n');
}

// 根据配置选择游戏
function selectGamesForDisplay(allGames, displayConfig, fallbackCategory = null) {
    if (!displayConfig) {
        console.warn('⚠️ No display config provided, using fallback');
        return fallbackCategory ?
            allGames.filter(game => game.category === fallbackCategory).slice(0, 6) :
            allGames.slice(0, 6);
    }

    const { count = 6, selection_method = 'sequential', selected_games = [] } = displayConfig;
    let selectedGames = [];

    switch (selection_method) {
        case 'configured':
            // 配置化选择：根据selected_games数组中的ID查找游戏
            selectedGames = selected_games
                .map(gameId => allGames.find(game => game.id === gameId))
                .filter(game => game !== undefined) // 过滤掉不存在的游戏
                .slice(0, count);

            // 如果配置的游戏数量不足，用顺序选择补充
            if (selectedGames.length < count && fallbackCategory) {
                const remainingCount = count - selectedGames.length;
                const fallbackGames = allGames
                    .filter(game => game.category === fallbackCategory)
                    .filter(game => !selectedGames.some(selected => selected.id === game.id))
                    .slice(0, remainingCount);
                selectedGames = [...selectedGames, ...fallbackGames];
            }

            console.log(`🎯 Configured selection: ${selectedGames.length}/${count} games selected`);
            break;

        case 'random':
            // 随机选择
            const availableGames = fallbackCategory ?
                allGames.filter(game => game.category === fallbackCategory) :
                allGames;
            const shuffled = [...availableGames].sort(() => Math.random() - 0.5);
            selectedGames = shuffled.slice(0, count);
            console.log(`🎲 Random selection: ${selectedGames.length} games selected`);
            break;

        case 'sequential':
        default:
            // 顺序选择（默认）
            const sequentialGames = fallbackCategory ?
                allGames.filter(game => game.category === fallbackCategory) :
                allGames;
            selectedGames = sequentialGames.slice(0, count);
            console.log(`📋 Sequential selection: ${selectedGames.length} games selected`);
            break;
    }

    return selectedGames;
}

// 生成Feature Blocks HTML
function generateFeatureBlocks(featureBlocks) {
    if (!featureBlocks || !Array.isArray(featureBlocks)) {
        return '';
    }



    const featureBlocksTemplate = readFile(path.join(CONFIG.componentsDir, 'feature-blocks.html'));

    return featureBlocks.map(block => {
        let blockHtml = '';
        

        switch (block.type) {
            case 'youtube':
                blockHtml = `
                <div class="feature-block youtube-block">
                    <h2>${block.title || 'Watch Gameplay'}</h2>
                    <div class="youtube-embed">
                        <iframe
                            src="https://www.youtube.com/embed/${block.videoId}"
                            title="${block.title || 'Game Video'}"
                            frameborder="0"
                            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                            allowfullscreen>
                        </iframe>
                    </div>
                </div>`;
                break;
                
            case 'section':
                // 检测format字段，支持paragraph（默认）和list两种格式
                const format = block.format || 'paragraph';
                let contentHtml = '';

                if (format === 'list') {
                    // list格式：按|分隔符拆分成列表项
                    const listItems = block.content.split('|').map(item => item.trim()).filter(item => item);
                    contentHtml = `<ol>${listItems.map(item => `<li>${item}</li>`).join('')}</ol>`;
                } else {
                    // paragraph格式（默认）：将|替换为句号+空格
                    const paragraphContent = block.content.replace(/\|/g, '. ');
                    contentHtml = `<p>${paragraphContent}</p>`;
                }

                blockHtml = `
                <div class="feature-block section-block">
                    <h2>${block.title}</h2>
                    ${contentHtml}
                </div>`;
                break;
                
            case 'heading':
                blockHtml = `
                <div class="feature-block heading-block">
                    <h2>${block.text}</h2>
                </div>`;
                break;
                
            case 'paragraph':
                blockHtml = `
                <div class="feature-block paragraph-block">
                    <p>${block.content}</p>
                </div>`;
                break;
                
            case 'custom_html':
                blockHtml = `
                <div class="feature-block custom-html-block">
                    ${block.html}
                </div>`;
                break;
                
            default:
                console.warn(`⚠️ Unknown feature block type: ${block.type}`);
        }
        

        return blockHtml;
    }).join('\n');
}

// 生成游戏搜索数据JavaScript文件
function generateGamesDataJS(gamesData) {
    const searchableGames = gamesData.listGames.map(game => ({
        id: game.id,
        name: game.name,
        category: game.category,
        thumbnail: game.thumbnail,
        url: game.category === 'popular' 
            ? `popular_games/${game.id}.html` 
            : `new_games/${game.id}.html`
    }));

    const jsContent = `// 游戏搜索数据 - 构建时自动生成
// 请勿手动修改此文件
window.GAMES_DATA = ${JSON.stringify(searchableGames, null, 2)};

// 搜索数据统计
console.log('🎮 Loaded', window.GAMES_DATA.length, 'games for search');
`;

    const jsFilePath = path.join(CONFIG.distDir, 'js', 'games-data.js');
    writeFile(jsFilePath, jsContent);
    console.log(`📊 Generated games search data: ${searchableGames.length} games`);
}

// ===== 游戏生成检查函数 =====

// 检查游戏是否应该生成HTML文件
function checkGameShouldGenerate(game, config) {
    if (game.category === 'popular') {
        return config.display_settings?.homepage?.popular_games_display?.enabled !== false;
    }
    if (game.category === 'new') {
        return config.display_settings?.homepage?.new_games_display?.enabled !== false;
    }
    // 其他类型的游戏默认生成（如homepage-special等）
    return true;
}

// 检查分类页面是否应该生成
function checkCategoryShouldGenerate(category, config) {
    if (category === 'popular') {
        return config.display_settings?.homepage?.popular_games_display?.enabled !== false;
    }
    if (category === 'new') {
        return config.display_settings?.homepage?.new_games_display?.enabled !== false;
    }
    return true;
}

// ===== Sitemap生成函数 =====

// 生成静态页面sitemap
function generateStaticSitemap(config) {
    const sitemapConfig = config.sitemap?.sitemaps?.static;
    if (!sitemapConfig?.enabled) {
        console.log('📄 Static sitemap disabled, skipping...');
        return false;
    }

    const baseUrl = config.sitemap?.base_url || 'https://gameport.example.com';
    const currentDate = new Date().toISOString();

    const urls = [
        {
            loc: baseUrl,
            lastmod: currentDate,
            changefreq: sitemapConfig.change_freq || 'daily',
            priority: sitemapConfig.priority?.homepage || '1.0'
        },
        {
            loc: `${baseUrl}/popular/`,
            lastmod: currentDate,
            changefreq: sitemapConfig.change_freq || 'daily',
            priority: sitemapConfig.priority?.category_pages || '0.8'
        },
        {
            loc: `${baseUrl}/new/`,
            lastmod: currentDate,
            changefreq: sitemapConfig.change_freq || 'daily',
            priority: sitemapConfig.priority?.category_pages || '0.8'
        }
    ];

    const sitemapXml = generateSitemapXml(urls);
    writeFile(path.join(CONFIG.distDir, 'sitemap-static.xml'), sitemapXml);
    console.log('✅ Generated: sitemap-static.xml');
    return true;
}

// 生成Popular Games sitemap
function generatePopularSitemap(config, games) {
    const sitemapConfig = config.sitemap?.sitemaps?.popular;
    const popularEnabled = config.display_settings?.homepage?.popular_games_display?.enabled !== false;

    if (!sitemapConfig?.enabled || !popularEnabled) {
        console.log('📄 Popular Games sitemap disabled, skipping...');
        return false;
    }

    const baseUrl = config.sitemap?.base_url || 'https://gameport.example.com';
    const currentDate = new Date().toISOString();

    // 只包含实际生成的Popular Games（使用相同的检查逻辑）
    const popularGames = games.filter(game =>
        game.category === 'popular' && checkGameShouldGenerate(game, config)
    );

    const urls = popularGames.map(game => ({
        loc: `${baseUrl}/popular_games/${game.id}.html`,
        lastmod: currentDate,
        changefreq: sitemapConfig.change_freq || 'weekly',
        priority: sitemapConfig.priority || '0.7'
    }));

    if (urls.length === 0) {
        console.log('📄 No Popular Games found, skipping sitemap generation...');
        return false;
    }

    const sitemapXml = generateSitemapXml(urls);
    writeFile(path.join(CONFIG.distDir, 'sitemap-popular.xml'), sitemapXml);
    console.log(`✅ Generated: sitemap-popular.xml (${urls.length} games)`);
    return true;
}

// 生成New Games sitemap
function generateNewSitemap(config, games) {
    const sitemapConfig = config.sitemap?.sitemaps?.new;
    const newEnabled = config.display_settings?.homepage?.new_games_display?.enabled !== false;

    if (!sitemapConfig?.enabled || !newEnabled) {
        console.log('📄 New Games sitemap disabled, skipping...');
        return false;
    }

    const baseUrl = config.sitemap?.base_url || 'https://gameport.example.com';
    const currentDate = new Date().toISOString();

    // 只包含实际生成的New Games（使用相同的检查逻辑）
    const newGames = games.filter(game =>
        game.category === 'new' && checkGameShouldGenerate(game, config)
    );

    const urls = newGames.map(game => ({
        loc: `${baseUrl}/new_games/${game.id}.html`,
        lastmod: currentDate,
        changefreq: sitemapConfig.change_freq || 'weekly',
        priority: sitemapConfig.priority || '0.6'
    }));

    if (urls.length === 0) {
        console.log('📄 No New Games found, skipping sitemap generation...');
        return false;
    }

    const sitemapXml = generateSitemapXml(urls);
    writeFile(path.join(CONFIG.distDir, 'sitemap-new.xml'), sitemapXml);
    console.log(`✅ Generated: sitemap-new.xml (${urls.length} games)`);
    return true;
}

// 生成法律页面sitemap
function generateLegalSitemap(config) {
    const sitemapConfig = config.sitemap?.sitemaps?.legal;
    if (!sitemapConfig?.enabled) {
        console.log('📄 Legal sitemap disabled, skipping...');
        return false;
    }

    const baseUrl = config.sitemap?.base_url || 'https://gameport.example.com';
    const currentDate = new Date().toISOString();

    const legalPages = [
        'Privacy-Policy.html',
        'Contact-Us.html',
        'About-Us.html',
        'DMCA.html'
    ];

    const urls = legalPages.map(page => ({
        loc: `${baseUrl}/legal_info/${page}`,
        lastmod: currentDate,
        changefreq: sitemapConfig.change_freq || 'yearly',
        priority: sitemapConfig.priority || '0.3'
    }));

    const sitemapXml = generateSitemapXml(urls);
    writeFile(path.join(CONFIG.distDir, 'sitemap-legal.xml'), sitemapXml);
    console.log(`✅ Generated: sitemap-legal.xml (${urls.length} pages)`);
    return true;
}

// 生成主Sitemap Index文件
function generateSitemapIndex(config, generatedSitemaps) {
    if (!config.sitemap?.generate_index || generatedSitemaps.length === 0) {
        console.log('📄 Sitemap index disabled or no sitemaps generated, skipping...');
        return false;
    }

    const baseUrl = config.sitemap?.base_url || 'https://gameport.example.com';
    const currentDate = new Date().toISOString();

    const sitemapEntries = generatedSitemaps.map(sitemapFile => `
    <sitemap>
        <loc>${baseUrl}/${sitemapFile}</loc>
        <lastmod>${currentDate}</lastmod>
    </sitemap>`).join('');

    const sitemapIndexXml = `<?xml version="1.0" encoding="UTF-8"?>
<sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">${sitemapEntries}
</sitemapindex>`;

    writeFile(path.join(CONFIG.distDir, 'sitemap.xml'), sitemapIndexXml);
    console.log(`✅ Generated: sitemap.xml (${generatedSitemaps.length} sitemaps)`);
    return true;
}

// 生成robots.txt文件
function generateRobotsTxt(config) {
    if (!config.sitemap?.enabled) {
        console.log('📄 Sitemap disabled, skipping robots.txt generation...');
        return false;
    }

    const baseUrl = config.sitemap?.base_url || 'https://gameport.example.com';

    const robotsTxt = `User-agent: *
Allow: /

# Sitemap
Sitemap: ${baseUrl}/sitemap.xml

# Disallow admin or sensitive areas (if any)
# Disallow: /admin/
# Disallow: /private/`;

    writeFile(path.join(CONFIG.distDir, 'robots.txt'), robotsTxt);
    console.log('✅ Generated: robots.txt');
    return true;
}

// 生成404页面
function generate404Page(config) {
    console.log('📄 Generating 404 page...');

    // 检查功能开关状态
    const newGamesEnabled = config.display_settings?.homepage?.new_games_display?.enabled !== false;
    const popularGamesEnabled = config.display_settings?.homepage?.popular_games_display?.enabled !== false;

    // 生成导航链接CSS类
    const newGamesNavClass = newGamesEnabled ? 'nav-link' : 'nav-link nav-disabled';
    const popularGamesNavClass = popularGamesEnabled ? 'nav-link' : 'nav-link nav-disabled';

    // 简化的404页面HTML
    const page404 = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>404 - Page Not Found | ${config.site_name || 'GamePort'}</title>
    <meta name="description" content="Sorry, the page you are looking for doesn't exist.">
    <meta name="keywords" content="404, page not found, error">
    <meta name="robots" content="noindex, nofollow">

    <!-- Canonical URL -->
    <link rel="canonical" href="${(config.seo?.canonical_base_url || config.site_url || 'https://gameport.example.com').replace(/\/$/, '')}/404.html">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="images/common/favicon.ico">

    <!-- CSS Files -->
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/themes/theme-${config.selected_theme || 'default'}.css">
</head>
<body class="popular-1col iframe-large">
    <!-- Header -->
    <header class="main-header">
        <nav class="navbar">
            <div class="nav-container">
                <div class="nav-brand">
                    <a href="/" class="brand-link">
                        <img src="images/common/logo.png" alt="${config.site_name || 'GamePort'}" class="logo">
                        <span class="brand-text">${config.site_name || 'GamePort'}</span>
                    </a>
                </div>

                <ul class="nav-menu">
                    <li class="nav-item">
                        <a href="/" class="nav-link">Home</a>
                    </li>
                    <li class="nav-item">
                        <a href="popular/" class="${popularGamesNavClass}">Popular Games</a>
                    </li>
                    <li class="nav-item">
                        <a href="new/" class="${newGamesNavClass}">New Games</a>
                    </li>
                </ul>

                <div class="nav-toggle">
                    <span class="bar"></span>
                    <span class="bar"></span>
                    <span class="bar"></span>
                </div>
            </div>
        </nav>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <div class="error-page">
            <div class="container">
                <div class="error-content">
                    <div class="error-number">404</div>
                    <h1 class="error-title">Page Not Found</h1>
                    <p class="error-description">Sorry, the page you are looking for doesn't exist.</p>
                    <div class="error-actions">
                        <a href="/" class="btn btn-primary">Go Home</a>
                        <a href="popular/" class="btn btn-secondary">Browse Games</a>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="main-footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>${config.site_name || 'GamePort'}</h3>
                    <p>Your ultimate destination for online games. Play the best collection of games right in your browser!</p>
                </div>

                <div class="footer-section">
                    <h4>Quick Links</h4>
                    <ul class="footer-links">
                        <li><a href="/">Home</a></li>
                        <li><a href="popular/">Popular Games</a></li>
                        <li><a href="new/">New Games</a></li>
                    </ul>
                </div>

                <div class="footer-section">
                    <h4>Legal</h4>
                    <ul class="footer-links">
                        <li><a href="legal_info/Privacy-Policy.html">Privacy Policy</a></li>
                        <li><a href="legal_info/Contact-Us.html">Contact Us</a></li>
                        <li><a href="legal_info/About-Us.html">About Us</a></li>
                        <li><a href="legal_info/DMCA.html">DMCA</a></li>
                    </ul>
                </div>
            </div>

            <div class="footer-bottom">
                <p>&copy; 2024 ${config.site_name || 'GamePort'}. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="js/main.js"></script>
</body>
</html>`;

    writeFile(path.join(CONFIG.distDir, '404.html'), page404);
    console.log('✅ Generated: 404.html');
    return true;
}

// 生成sitemap XML内容
function generateSitemapXml(urls) {
    const urlEntries = urls.map(url => `
    <url>
        <loc>${url.loc}</loc>
        <lastmod>${url.lastmod}</lastmod>
        <changefreq>${url.changefreq}</changefreq>
        <priority>${url.priority}</priority>
    </url>`).join('');

    return `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">${urlEntries}
</urlset>`;
}

// 主构建函数
function build() {
    console.log('🚀 Starting GamePort build process...\n');
    
    // 创建输出目录
    ensureDirectoryExists(CONFIG.distDir);
    
    // 复制静态资源
    console.log('📋 Copying static assets...');
    copyDirectory(path.join(CONFIG.srcDir, 'css'), path.join(CONFIG.distDir, 'css'));
    copyDirectory(path.join(CONFIG.srcDir, 'js'), path.join(CONFIG.distDir, 'js'));
    copyDirectory(path.join(CONFIG.srcDir, 'images'), path.join(CONFIG.distDir, 'images'));

    // 注意：legal_info目录将在后面单独处理，不在这里复制
    
    // 读取游戏数据和模板
    const gamesData = loadGamesData();
    
    // 生成游戏搜索数据
    generateGamesDataJS(gamesData);
    
    const mainTemplate = readFile(path.join(CONFIG.templatesDir, '_main-layout-template.html'));
    const gameTemplate = readFile(path.join(CONFIG.templatesDir, 'game-detail-template.html'));

    if (!mainTemplate) {
        console.error('❌ Main template not found!');
        return;
    }

    if (!gameTemplate) {
        console.error('❌ Game template not found!');
        return;
    }
    
    console.log('\n🎮 Generating pages...');

    // 读取配置文件
    const config = loadConfig();

    // 使用列表游戏进行游戏选择（不包含首页专用游戏）
    const listGames = gamesData.listGames; // 只使用列表游戏

    // 检查功能开关状态
    const newGamesEnabled = config.display_settings?.homepage?.new_games_display?.enabled !== false;
    const popularGamesEnabled = config.display_settings?.homepage?.popular_games_display?.enabled !== false;

    console.log(`🎮 New Games enabled: ${newGamesEnabled}`);
    console.log(`🎮 Popular Games enabled: ${popularGamesEnabled}`);

    // 显示将要生成的内容概览
    console.log('\n📋 Build Configuration:');
    console.log(`   📄 Homepage: Always generated`);
    console.log(`   📄 Popular category pages: ${popularGamesEnabled ? 'Enabled' : 'Disabled'}`);
    console.log(`   📄 New category pages: ${newGamesEnabled ? 'Enabled' : 'Disabled'}`);
    console.log(`   📄 Popular game detail pages: ${popularGamesEnabled ? 'Enabled' : 'Disabled'}`);
    console.log(`   📄 New game detail pages: ${newGamesEnabled ? 'Enabled' : 'Disabled'}`);
    console.log(`   🗺️ Sitemap generation: ${config.sitemap?.enabled !== false ? 'Enabled' : 'Disabled'}`);

    // 根据配置选择要显示的游戏（只有在enabled时才生成）
    const selectedPopularGames = popularGamesEnabled ? selectGamesForDisplay(
        listGames,
        config.display_settings?.homepage?.popular_games_display,
        'popular'
    ) : [];

    const selectedNewGames = newGamesEnabled ? selectGamesForDisplay(
        listGames,
        config.display_settings?.homepage?.new_games_display,
        'new'
    ) : [];

    // 生成游戏列表HTML（如果禁用则返回空字符串）
    const popularGamesList = popularGamesEnabled ?
        selectedPopularGames.map(game => generateGameCard(game)).join('\n') : '';
    const newGamesList = newGamesEnabled ?
        selectedNewGames.map(game => generateGameCard(game)).join('\n') : '';

    console.log(`🎯 Homepage Popular Games: ${popularGamesEnabled ? selectedPopularGames.length + ' games' : 'disabled'}`);
    console.log(`🎯 Homepage New Games: ${newGamesEnabled ? selectedNewGames.length + ' games' : 'disabled'}`);

    // 生成主页（使用首页专用游戏数据）
    generateHomePage(mainTemplate, gamesData.homepageGame, popularGamesList, newGamesList, config);

    // 生成列表页面（仅为启用的游戏类型生成）
    if (checkCategoryShouldGenerate('popular', config)) {
        generateListPage(gamesData.listGames, 'popular');
        console.log('✅ Generated: popular category pages');
    } else {
        console.log('⏭️ Skipped: popular category pages (Popular Games disabled)');
    }

    if (checkCategoryShouldGenerate('new', config)) {
        generateListPage(gamesData.listGames, 'new');
        console.log('✅ Generated: new category pages');
    } else {
        console.log('⏭️ Skipped: new category pages (New Games disabled)');
    }

    // 生成游戏页面（仅为启用的游戏类型生成详情页）
    let generatedGamesCount = 0;
    let skippedGamesCount = 0;

    gamesData.listGames.forEach(game => {
        if (checkGameShouldGenerate(game, config)) {
            generateGamePage(gameTemplate, game, popularGamesList, newGamesList, config);
            generatedGamesCount++;
        } else {
            console.log(`⏭️ Skipped: ${game.category}/${game.id}.html (${game.category} games disabled)`);
            skippedGamesCount++;
        }
    });

    console.log(`🎮 Generated ${generatedGamesCount} game pages, skipped ${skippedGamesCount} pages`);

    // 生成法律信息页面
    generateLegalPages();

    // 生成404页面
    generate404Page(config);

    // 生成Sitemap文件
    if (config.sitemap?.enabled !== false) {
        console.log('\n🗺️ Generating sitemaps...');
        const generatedSitemaps = [];

        // 生成各个子sitemap
        if (generateStaticSitemap(config)) {
            generatedSitemaps.push('sitemap-static.xml');
        }

        if (generatePopularSitemap(config, gamesData.listGames)) {
            generatedSitemaps.push('sitemap-popular.xml');
        }

        if (generateNewSitemap(config, gamesData.listGames)) {
            generatedSitemaps.push('sitemap-new.xml');
        }

        if (generateLegalSitemap(config)) {
            generatedSitemaps.push('sitemap-legal.xml');
        }

        // 生成主sitemap index文件
        generateSitemapIndex(config, generatedSitemaps);

        // 生成robots.txt
        generateRobotsTxt(config);

        console.log(`🗺️ Sitemap generation completed! Generated ${generatedSitemaps.length} sitemaps.`);
    } else {
        console.log('🗺️ Sitemap generation disabled in config.');
    }

    console.log('\n🎉 Build completed successfully!');
    console.log(`📁 Output directory: ${CONFIG.distDir}`);
}

// 生成主页
function generateHomePage(template, homepageGame, popularGamesList, newGamesList, config) {
    // 检查功能开关状态
    const newGamesEnabled = config.display_settings?.homepage?.new_games_display?.enabled !== false;
    const popularGamesEnabled = config.display_settings?.homepage?.popular_games_display?.enabled !== false;

    // 获取 Popular Games 列数配置
    const popularColumns = config.display_settings?.homepage?.popular_games_display?.columns || 1;
    const popularColumnsClass = `popular-${popularColumns}col`;

    // 获取 iframe 尺寸配置
    const iframeSize = config.iframe_settings?.default_size || 'small';
    const iframeSizeClass = `iframe-${iframeSize}`;
    const iframeConfig = config.iframe_settings?.sizes?.[iframeSize] || config.iframe_settings?.sizes?.small;

    // 生成动态CSS类
    let dynamicClasses = [popularColumnsClass, iframeSizeClass];
    if (!newGamesEnabled) {
        dynamicClasses.push('no-new-games');
    }
    if (!popularGamesEnabled) {
        dynamicClasses.push('no-popular-games');
    }
    const dynamicLayoutClasses = dynamicClasses.join(' ');

    // 生成导航链接CSS类
    const newGamesNavClass = newGamesEnabled ? 'nav-link' : 'nav-link nav-disabled';
    const popularGamesNavClass = popularGamesEnabled ? 'nav-link' : 'nav-link nav-disabled';

    console.log(`🎯 Popular Games columns: ${popularColumns} (class: ${popularColumnsClass})`);
    console.log(`🖼️ iframe size: ${iframeSize} (${iframeConfig?.width}×${iframeConfig?.height}) (class: ${iframeSizeClass})`);

    // 生成首页专用游戏的feature blocks内容

    const featureContent = homepageGame ? generateFeatureBlocks(homepageGame.feature_blocks) :
        '<div class="welcome-message"><h2>Welcome to GamePort</h2><p>Select a game to start playing!</p></div>';

    // 条件渲染游戏区域HTML
    let processedTemplate = template;

    // 处理New Games区域
    if (!newGamesEnabled) {
        // 移除New Games Section
        processedTemplate = processedTemplate.replace(
            /<!-- New Games Section -->[\s\S]*?<\/section>/,
            ''
        );
    }

    // 处理Popular Games区域（移动端和侧边栏）
    if (!popularGamesEnabled) {
        // 移除Popular Games Section (Mobile Only)
        processedTemplate = processedTemplate.replace(
            /<!-- Popular Games Section \(Mobile Only\) -->[\s\S]*?<\/section>/,
            ''
        );
        // 移除Right Sidebar中的Popular Games Section
        processedTemplate = processedTemplate.replace(
            /<!-- Popular Games Section -->[\s\S]*?<\/section>/,
            ''
        );
    }

    let homePage = processedTemplate
        .replace(/\{\{GAME_URL\}\}/g, homepageGame ? homepageGame.gameUrl : 'about:blank')
        .replace(/\{\{GAME_ID\}\}/g, homepageGame ? homepageGame.id : 'homepage-default')
        .replace(/\{\{GAME_TITLE\}\}/g, homepageGame ? homepageGame.name : 'Welcome to GamePort')
        .replace(/\{\{GAME_THUMBNAIL\}\}/g, homepageGame ? homepageGame.thumbnail : 'images/common/default-game.png')
        .replace(/\{\{POPULAR_GAMES_LIST\}\}/g, popularGamesList)
        .replace(/\{\{NEW_GAMES_LIST\}\}/g, newGamesList)
        .replace(/\{\{GAME_FEATURE_CONTENT\}\}/g, featureContent)
        .replace(/\{\{PAGE_TITLE\}\}/g, homepageGame && homepageGame.meta ? homepageGame.meta.title : 'GamePort - Your Ultimate Game Destination')
        .replace(/\{\{PAGE_DESCRIPTION\}\}/g, homepageGame && homepageGame.meta ? homepageGame.meta.description : 'Discover and play the best online games for free!')
        .replace(/\{\{PAGE_KEYWORDS\}\}/g, 'online games, free games, browser games')
        .replace(/\{\{SITE_AUTHOR\}\}/g, config.seo?.author || 'GamePort Team')
        .replace(/\{\{SITE_URL\}\}/g, config.site_url || 'https://gameport.example.com')
        .replace(/\{\{SITE_NAME\}\}/g, config.site_name || 'GamePort')
        .replace(/\{\{CANONICAL_URL\}\}/g, (config.seo?.canonical_base_url || config.site_url || 'https://gameport.example.com').replace(/\/$/, '') + '/')
        .replace(/\{\{THEME_CSS_PATH\}\}/g, `css/themes/theme-${config.selected_theme || 'default'}.css`)
        .replace(/\{\{POPULAR_COLUMNS_CLASS\}\}/g, popularColumnsClass)
        .replace(/\{\{IFRAME_SIZE_CLASS\}\}/g, iframeSizeClass)
        .replace(/\{\{IFRAME_WIDTH\}\}/g, iframeConfig?.width || 854)
        .replace(/\{\{IFRAME_HEIGHT\}\}/g, iframeConfig?.height || 480)
        .replace(/\{\{DYNAMIC_LAYOUT_CLASSES\}\}/g, dynamicLayoutClasses)
        .replace(/\{\{NEW_GAMES_NAV_CLASS\}\}/g, newGamesNavClass)
        .replace(/\{\{POPULAR_GAMES_NAV_CLASS\}\}/g, popularGamesNavClass)
        .replace(/\{\{HOME_ACTIVE\}\}/g, '')
        .replace(/\{\{POPULAR_ACTIVE\}\}/g, '')
        .replace(/\{\{NEW_ACTIVE\}\}/g, '');

    writeFile(path.join(CONFIG.distDir, 'index.html'), homePage);
}

// 生成分页导航HTML
function generatePaginationHTML(currentPage, totalPages, category) {
    if (totalPages <= 1) {
        return ''; // 不显示分页导航
    }

    let paginationHTML = `
    <nav class="pagination-nav" aria-label="${category} games pagination">
        <div class="container">
            <div class="pagination">`;

    // Previous按钮
    if (currentPage > 1) {
        const prevUrl = currentPage === 2 ? 'index.html' : `page-${currentPage - 1}.html`;
        paginationHTML += `
                <a href="${prevUrl}" class="pagination-btn pagination-prev">
                    <span>‹</span> Previous
                </a>`;
    }

    // 页码数字
    paginationHTML += `
                <div class="pagination-numbers">`;

    // 显示前3页
    for (let i = 1; i <= Math.min(3, totalPages); i++) {
        const url = i === 1 ? 'index.html' : `page-${i}.html`;
        const activeClass = i === currentPage ? ' active' : '';
        paginationHTML += `
                    <a href="${url}" class="pagination-number${activeClass}">${i}</a>`;
    }

    // 显示省略号和最后一页
    if (totalPages > 5) {
        paginationHTML += `
                    <span class="pagination-dots">...</span>`;
        const url = `page-${totalPages}.html`;
        const activeClass = totalPages === currentPage ? ' active' : '';
        paginationHTML += `
                    <a href="${url}" class="pagination-number${activeClass}">${totalPages}</a>`;
    } else if (totalPages > 3) {
        // 如果总页数4-5页，显示所有页码
        for (let i = 4; i <= totalPages; i++) {
            const url = `page-${i}.html`;
            const activeClass = i === currentPage ? ' active' : '';
            paginationHTML += `
                    <a href="${url}" class="pagination-number${activeClass}">${i}</a>`;
        }
    }

    paginationHTML += `
                </div>`;

    // Next按钮
    if (currentPage < totalPages) {
        const nextUrl = `page-${currentPage + 1}.html`;
        paginationHTML += `
                <a href="${nextUrl}" class="pagination-btn pagination-next">
                    Next <span>›</span>
                </a>`;
    }

    paginationHTML += `
            </div>
        </div>
    </nav>`;

    return paginationHTML;
}

// 生成列表页面（支持分页）
function generateListPage(games, category) {
    console.log(`📄 Generating ${category} pages...`);

    const GAMES_PER_PAGE = 60; // 6列 × 10行
    const categoryGames = games.filter(game => game.category === category);
    const totalPages = Math.ceil(categoryGames.length / GAMES_PER_PAGE);

    // 创建分类文件夹
    const categoryDir = path.join(CONFIG.distDir, category);
    ensureDirectoryExists(categoryDir);

    // 使用专门的列表页面模板
    const listTemplate = readFile(path.join(CONFIG.templatesDir, '_list-layout-template.html'));

    if (!listTemplate) {
        console.error('❌ List template not found!');
        return;
    }

    // 读取配置文件
    const config = loadConfig();

    // 获取 Popular Games 列数配置
    const popularColumns = config.display_settings?.homepage?.popular_games_display?.columns || 1;
    const popularColumnsClass = `popular-${popularColumns}col`;

    // 设置页面信息
    const pageInfo = {
        'popular': {
            title: 'Popular Games',
            subtitle: 'Play the most popular and trending games on GamePort. Discover top-rated games loved by millions of players worldwide.',
            activeClass: 'active'
        },
        'new': {
            title: 'New Games',
            subtitle: 'Discover the latest and newest games added to GamePort. Fresh content updated regularly with trending games and exciting new releases.',
            activeClass: 'active'
        }
    };

    const info = pageInfo[category];
    if (!info) {
        console.error(`❌ Unknown category: ${category}`);
        return;
    }

    // 生成每一页
    for (let page = 1; page <= totalPages; page++) {
        const startIndex = (page - 1) * GAMES_PER_PAGE;
        const endIndex = startIndex + GAMES_PER_PAGE;
        const pageGames = categoryGames.slice(startIndex, endIndex);

        // 文件名规则
        const fileName = page === 1 ? 'index.html' : `page-${page}.html`;
        const filePath = path.join(categoryDir, fileName);

        // 生成游戏列表HTML（修正相对路径）
        let categoryGamesList = pageGames.map(game => generateGameCard(game)).join('\n');

        // 修正分类页面中的游戏卡片链接路径
        categoryGamesList = categoryGamesList
            .replace(/href="popular_games\//g, 'href="../popular_games/')
            .replace(/href="new_games\//g, 'href="../new_games/')
            .replace(/src="images\//g, 'src="../images/');

        // 生成分页导航HTML
        const paginationHTML = generatePaginationHTML(page, totalPages, category);

        // 页面标题（包含页码）
        const pageTitle = page === 1 ? info.title : `${info.title} - Page ${page}`;
        const pageDescription = page === 1 ? info.subtitle : `${info.subtitle} Browse page ${page} of ${totalPages}.`;

        // 生成页面HTML
        let listPage = listTemplate
            .replace(/\{\{PAGE_TITLE\}\}/g, pageTitle)
            .replace(/\{\{PAGE_MAIN_TITLE\}\}/g, info.title)
            .replace(/\{\{PAGE_SUBTITLE\}\}/g, info.subtitle)
            .replace(/\{\{PAGE_DESCRIPTION\}\}/g, pageDescription)
            .replace(/\{\{PAGE_KEYWORDS\}\}/g, `${category} games, online games, free games`)
            .replace(/\{\{SITE_AUTHOR\}\}/g, config.seo?.author || 'GamePort Team')
            .replace(/\{\{SITE_URL\}\}/g, config.site_url || 'https://gameport.example.com')
            .replace(/\{\{CANONICAL_URL\}\}/g, (config.seo?.canonical_base_url || config.site_url || 'https://gameport.example.com').replace(/\/$/, '') + `/${category}/${fileName}`)
            .replace(/\{\{THEME_CSS_PATH\}\}/g, `../css/themes/theme-${config.selected_theme || 'default'}.css`)
            .replace(/\{\{SITE_NAME\}\}/g, config.site_name || 'GamePort')
            .replace(/\{\{GAMES_LIST\}\}/g, categoryGamesList)
            .replace(/\{\{PAGINATION_HTML\}\}/g, paginationHTML)
            .replace(/\{\{POPULAR_COLUMNS_CLASS\}\}/g, popularColumnsClass)
            .replace(/\{\{BREADCRUMB_CURRENT\}\}/g, info.title)
            .replace(/\{\{HOME_ACTIVE\}\}/g, '')
            .replace(/\{\{POPULAR_ACTIVE\}\}/g, category === 'popular' ? info.activeClass : '')
            .replace(/\{\{NEW_ACTIVE\}\}/g, category === 'new' ? info.activeClass : '');

        // 修正分类页面中的CSS和其他资源路径
        listPage = listPage
            .replace(/href="css\/style\.css"/g, 'href="../css/style.css"')
            .replace(/href="images\//g, 'href="../images/')
            .replace(/src="images\//g, 'src="../images/')
            .replace(/href="js\//g, 'href="../js/')
            .replace(/src="js\//g, 'src="../js/');

        writeFile(filePath, listPage);
        console.log(`✅ Generated: ${category}/${fileName}`);
    }

    console.log(`📊 Generated ${totalPages} pages for ${category} (${categoryGames.length} games, ${GAMES_PER_PAGE} per page)`);
}

// 生成游戏页面
function generateGamePage(template, game, popularGamesList, newGamesList, config) {
    const featureContent = generateFeatureBlocks(game.feature_blocks);

    // 检查功能开关状态
    const newGamesEnabled = config.display_settings?.homepage?.new_games_display?.enabled !== false;
    const popularGamesEnabled = config.display_settings?.homepage?.popular_games_display?.enabled !== false;

    // 获取 Popular Games 列数配置
    const popularColumns = config.display_settings?.homepage?.popular_games_display?.columns || 1;
    const popularColumnsClass = `popular-${popularColumns}col`;

    // 获取 iframe 尺寸配置
    const iframeSize = config.iframe_settings?.default_size || 'small';
    const iframeSizeClass = `iframe-${iframeSize}`;
    const iframeConfig = config.iframe_settings?.sizes?.[iframeSize] || config.iframe_settings?.sizes?.small;

    // 生成动态CSS类
    let dynamicClasses = [popularColumnsClass, iframeSizeClass];
    if (!newGamesEnabled) {
        dynamicClasses.push('no-new-games');
    }
    if (!popularGamesEnabled) {
        dynamicClasses.push('no-popular-games');
    }
    const dynamicLayoutClasses = dynamicClasses.join(' ');

    // 生成导航链接CSS类
    const newGamesNavClass = newGamesEnabled ? 'nav-link' : 'nav-link nav-disabled';
    const popularGamesNavClass = popularGamesEnabled ? 'nav-link' : 'nav-link nav-disabled';

    // 确定游戏页面的目录
    const gameDir = game.category === 'popular' ? 'popular_games' : 'new_games';
    const gameDirPath = path.join(CONFIG.distDir, gameDir);

    // 确保游戏目录存在
    ensureDirectoryExists(gameDirPath);

    // 条件渲染游戏区域HTML
    let processedTemplate = template;

    // 处理New Games区域
    if (!newGamesEnabled) {
        // 移除New Games Section
        processedTemplate = processedTemplate.replace(
            /<!-- New Games Section -->[\s\S]*?<\/section>/,
            ''
        );
    }

    // 处理Popular Games区域（移动端和侧边栏）
    if (!popularGamesEnabled) {
        // 移除Popular Games Section (Mobile Only)
        processedTemplate = processedTemplate.replace(
            /<!-- Popular Games Section \(Mobile Only\) -->[\s\S]*?<\/section>/,
            ''
        );
        // 移除Right Sidebar中的Popular Games Section
        processedTemplate = processedTemplate.replace(
            /<!-- Popular Games Section -->[\s\S]*?<\/section>/,
            ''
        );
    }

    // 替换所有占位符
    let gamePage = processedTemplate
        .replace(/\{\{GAME_URL\}\}/g, game.gameUrl || 'about:blank')
        .replace(/\{\{GAME_ID\}\}/g, game.id)
        .replace(/\{\{GAME_TITLE\}\}/g, game.name)
        .replace(/\{\{GAME_THUMBNAIL\}\}/g, game.thumbnail || 'images/common/default-game.png')
        .replace(/\{\{POPULAR_GAMES_LIST\}\}/g, popularGamesList)
        .replace(/\{\{NEW_GAMES_LIST\}\}/g, newGamesList)
        .replace(/\{\{GAME_FEATURE_CONTENT\}\}/g, featureContent)
        .replace(/\{\{PAGE_TITLE\}\}/g, game.meta ? game.meta.title : `${game.name} - Play Online Free | GamePort`)
        .replace(/\{\{PAGE_DESCRIPTION\}\}/g, game.meta ? game.meta.description : `Play ${game.name} online for free at GamePort. No downloads required!`)
        .replace(/\{\{PAGE_KEYWORDS\}\}/g, `${game.name}, online game, free game, ${game.category}`)
        .replace(/\{\{SITE_AUTHOR\}\}/g, config.seo?.author || 'GamePort Team')
        .replace(/\{\{SITE_URL\}\}/g, config.site_url || 'https://gameport.example.com')
        .replace(/\{\{SITE_NAME\}\}/g, config.site_name || 'GamePort')
        .replace(/\{\{CANONICAL_URL\}\}/g, (config.seo?.canonical_base_url || config.site_url || 'https://gameport.example.com').replace(/\/$/, '') + `/${gameDir}/${game.id}.html`)
        .replace(/\{\{THEME_CSS_PATH\}\}/g, `../css/themes/theme-${config.selected_theme || 'default'}.css`)
        .replace(/\{\{POPULAR_COLUMNS_CLASS\}\}/g, popularColumnsClass)
        .replace(/\{\{IFRAME_SIZE_CLASS\}\}/g, iframeSizeClass)
        .replace(/\{\{IFRAME_WIDTH\}\}/g, iframeConfig?.width || 854)
        .replace(/\{\{IFRAME_HEIGHT\}\}/g, iframeConfig?.height || 480)
        .replace(/\{\{DYNAMIC_LAYOUT_CLASSES\}\}/g, dynamicLayoutClasses)
        .replace(/\{\{NEW_GAMES_NAV_CLASS\}\}/g, newGamesNavClass)
        .replace(/\{\{POPULAR_GAMES_NAV_CLASS\}\}/g, popularGamesNavClass)
        .replace(/\{\{HOME_ACTIVE\}\}/g, '')
        .replace(/\{\{POPULAR_ACTIVE\}\}/g, '')
        .replace(/\{\{NEW_ACTIVE\}\}/g, '');

    // 然后修复游戏页面的相对路径
    gamePage = gamePage
        .replace(/\{\{HOME_ACTIVE\}\}/g, '')
        .replace(/\{\{POPULAR_ACTIVE\}\}/g, '')
        .replace(/\{\{NEW_ACTIVE\}\}/g, '');

    // 然后修复游戏页面的相对路径
    gamePage = gamePage
        .replace(/href="index\.html"/g, 'href="../index.html"')
        .replace(/href="popular\/"/g, 'href="../popular/"')
        .replace(/href="new\/"/g, 'href="../new/"')
        .replace(/src="images\//g, 'src="../images/')
        .replace(/href="css\/style\.css"/g, 'href="../css/style.css"')
        .replace(/href="popular_games\//g, 'href="../popular_games/')
        .replace(/href="new_games\//g, 'href="../new_games/');

    writeFile(path.join(gameDirPath, `${game.id}.html`), gamePage);
    console.log(`✅ Generated: dist\\${gameDir}\\${game.id}.html`);
}

// 复制法律信息页面
function generateLegalPages() {
    console.log('📄 Copying legal pages...');

    // 创建legal_info目录
    const legalDir = path.join(CONFIG.distDir, 'legal_info');
    ensureDirectoryExists(legalDir);

    // 复制并处理Privacy Policy页面
    copyAndProcessLegalPage(legalDir);
}

// 复制并处理法律页面
function copyAndProcessLegalPage(legalDir) {
    // 读取配置文件
    const config = loadConfig();
    const srcLegalDir = path.join(CONFIG.srcDir, 'legal_info');

    // 处理Privacy Policy页面
    const srcPrivacyPath = path.join(srcLegalDir, 'Privacy-Policy.html');
    if (fs.existsSync(srcPrivacyPath)) {
        let privacyContent = readFile(srcPrivacyPath);
        privacyContent = processLegalPageContent(privacyContent, config, 'Privacy-Policy.html');
        const destPrivacyPath = path.join(legalDir, 'Privacy-Policy.html');
        writeFile(destPrivacyPath, privacyContent);
        console.log(`✅ Copied and processed: legal_info/Privacy-Policy.html`);
    } else {
        console.warn(`⚠️ Source Privacy Policy file not found: ${srcPrivacyPath}`);
    }

    // 处理Contact Us页面
    const srcContactPath = path.join(srcLegalDir, 'Contact-Us.html');
    if (fs.existsSync(srcContactPath)) {
        let contactContent = readFile(srcContactPath);
        contactContent = processLegalPageContent(contactContent, config, 'Contact-Us.html');
        const destContactPath = path.join(legalDir, 'Contact-Us.html');
        writeFile(destContactPath, contactContent);
        console.log(`✅ Copied and processed: legal_info/Contact-Us.html`);
    } else {
        console.warn(`⚠️ Source Contact Us file not found: ${srcContactPath}`);
    }

    // 处理About Us页面
    const srcAboutPath = path.join(srcLegalDir, 'About-Us.html');
    if (fs.existsSync(srcAboutPath)) {
        let aboutContent = readFile(srcAboutPath);
        aboutContent = processLegalPageContent(aboutContent, config, 'About-Us.html');
        const destAboutPath = path.join(legalDir, 'About-Us.html');
        writeFile(destAboutPath, aboutContent);
        console.log(`✅ Copied and processed: legal_info/About-Us.html`);
    } else {
        console.warn(`⚠️ Source About Us file not found: ${srcAboutPath}`);
    }

    // 处理DMCA页面
    const srcDmcaPath = path.join(srcLegalDir, 'DMCA.html');
    if (fs.existsSync(srcDmcaPath)) {
        let dmcaContent = readFile(srcDmcaPath);
        dmcaContent = processLegalPageContent(dmcaContent, config, 'DMCA.html');
        const destDmcaPath = path.join(legalDir, 'DMCA.html');
        writeFile(destDmcaPath, dmcaContent);
        console.log(`✅ Copied and processed: legal_info/DMCA.html`);
    } else {
        console.warn(`⚠️ Source DMCA file not found: ${srcDmcaPath}`);
    }
}

// 处理法律页面内容的通用函数
function processLegalPageContent(content, config, fileName) {
    return content
        .replace(/\{\{SITE_NAME\}\}/g, config.site_name || 'GamePort')
        .replace(/\{\{SITE_AUTHOR\}\}/g, config.seo?.author || 'GamePort Team')
        .replace(/\{\{SITE_URL\}\}/g, config.site_url || 'https://gameport.example.com')
        .replace(/\{\{CANONICAL_URL\}\}/g, (config.seo?.canonical_base_url || config.site_url || 'https://gameport.example.com').replace(/\/$/, '') + `/legal_info/${fileName}`)
        .replace(/\{\{THEME_CSS_PATH\}\}/g, `../css/themes/theme-${config.selected_theme || 'default'}.css`)
        .replace(/\{\{CONTACT_EMAIL\}\}/g, config.contact?.email || '<EMAIL>')
        .replace(/\{\{RESPONSE_TIME\}\}/g, config.contact?.response_time || '24-48 hours');
}

// 运行构建
if (require.main === module) {
    build();
}

module.exports = { build };
