# 游戏门户网站产品需求文档 (PRD)

| 项目名称 | 静态游戏门户网站 (Project GamePort) |
| :--- | :--- |
| **版本** | `1.7 (最终定稿)` |
| **日期** | `2025年08月06日` |
| **制定人** | Gemini |
| **状态** | `定稿` |

## 修订历史

| 版本 | 日期 | 修订人 | 修订说明 |
| :--- | :--- | :--- | :--- |
| 1.0 | 2025-08-05 | Gemini | 初始文档创建。 |
| 1.1 | 2025-08-06 | Gemini | 增加了主题切换、动态内容块、部署流程等需求。 |
| 1.2 | 2025-08-06 | Gemini | 新增了分类列表页、响应式设计细化、广告位预留和图片文件夹结构等需求。 |
| 1.3 | 2025-08-06 | Gemini | 修正了游戏详情页的布局，使其与首页共享完整的组件结构。 |
| 1.4 | 2025-08-06 | Gemini | 采纳新方案：`games.json`中的第一个对象指定为首页专属内容的数据源。 |
| 1.5 | 2025-08-06 | Gemini | 优化并确定了`feature_blocks`的数据结构。 |
| 1.6 | 2025-08-06 | Gemini | 最终确定 `config.json` 结构，增加 `available_themes` 列表。 |
| 1.7 | 2025-08-06 | Gemini | **最终确认了所有页面布局和`feature_blocks`的灵活性。文档定稿。** |

## 1\. 项目概述

### 1.1. 项目愿景

打造一个高性能、设计精美、对搜索引擎极致友好、且维护流程极其简便的静态游戏门户网站。项目旨在为用户提供流畅的游戏体验，并为网站所有者提供一个可轻松扩展的游戏内容平台。

### 1.2. 核心目标

  * **卓越的用户体验：** 网站加载速度快，界面响应迅速，主题风格可定制。
  * **最佳的SEO实践：** 确保每个游戏页面都能被搜索引擎高效、独立地索引。
  * **极简的维护成本：** 新增或修改游戏内容应通过修改数据文件和执行自动化脚本来完成，无需直接编辑HTML页面。

## 2\. 总体设计与架构

### 2.1. 技术栈

  * **前端：** `HTML5`, `CSS3` (采用CSS变量体系), `JavaScript (ES6+)`
  * **构建环境：** `Node.js` (用于运行自动化构建脚本)

### 2.2. 核心架构：静态站点生成 (SSG)

本项目不采用传统的服务器端动态渲染或纯客户端渲染方案。所有页面都将通过一个Node.js构建脚本在开发环境中预先生成为内容完整的静态`.html`文件。这一架构是实现本项目所有核心目标的基础。

### 2.3. 全局配置文件 (`config.json`)

项目根目录下存在一个`config.json`文件，用于管理全站级别的配置。

  * **用途：** 定义网站名称、所有可用主题的列表以及当前选定的主题。
  * **结构示例：**
    ```json
    {
      "site_name": "GamePort - Your Ultimate Game Destination",
      "available_themes": [
        "default",
        "retro",
        "cyberpunk",
        "forest",
        "ocean",
        "battle"
      ],
      "selected_theme": "cyberpunk"
    }
    ```

### 2.4. 数据管理 (`games.json`)

所有游戏内容统一由`games.json`文件管理。

  * **核心约定：**

    1.  **首页内容：** 数组的第一个对象`[0]`的数据，专门用于生成`index.html`的核心内容。
    2.  **列表游戏：** 数组从第二个对象`[1]`开始为列表游戏。
    3.  **`feature_blocks`灵活性：** **所有游戏对象（包括首页对象）** 的`feature_blocks`数组都可以包含任意数量、任意顺序的`youtube`和`section`块。

  * **`games.json` 完整带注释示例：**

    ```json
    [
      {
        "//": "对象[0]: 这个对象的数据只用于生成index.html的核心内容。其feature_blocks同样支持任意数量的内容块。",
        "id": "homepage-special",
        "name": "本周精选：星际远航",
        "category": "popular",
        "thumbnail": "images/common/homepage-game.png",
        "gameUrl": "/games-source/space-odyssey/index.html",
        "feature_blocks": [
          {
            "type": "youtube",
            "videoId": "some-video-id",
            "title": "Explore the Galaxy in Space Odyssey!"
          },
          {
            "type": "section",
            "title": "About Our Featured Game",
            "content": "This week's featured game, Space Odyssey, takes you on an epic journey through the stars. Pilot your ship, battle alien fleets, and explore unknown galaxies."
          },
          {
            "type": "section",
            "title": "Core Mechanics",
            "content": "The game features realistic physics, resource management, and a branching narrative. Your choices matter!"
          }
        ],
        "meta": {
          "title": "欢迎来到GamePort | 玩转最新、最热的网页游戏",
          "description": "GamePort是您的终极游戏门户，探索海量精彩游戏。"
        }
      },
      {
        "//": "对象[1]: 这个游戏会出现在游戏列表中，并生成自己的详情页。",
        "id": "super-mario-bros",
        "name": "Super Mario Bros",
        "category": "popular",
        "thumbnail": "images/popular_games_image/super-mario-bros.png",
        "gameUrl": "/games-source/mario/index.html",
        "feature_blocks": [
          {
            "type": "youtube",
            "videoId": "rLl9XBg7wSs",
            "title": "Super Mario Bros Gameplay"
          },
          {
            "type": "section",
            "title": "What is Super Mario Bros",
            "content": "Super Mario Bros is a legendary platform adventure game that revolutionized the gaming industry. It follows Mario on his quest to rescue Princess Peach from the evil Bowser."
          },
          {
            "type": "section",
            "title": "How to Play",
            "content": "Use the arrow keys or WASD to move Mario left and right, and press the spacebar or up arrow to jump. Your goal is to reach the flagpole at the end of each level."
          }
        ],
        "meta": {
          "title": "Play Super Mario Bros Online | GamePort",
          "description": "Relive the classic adventure! Play the original Super Mario Bros online for free."
        }
      }
    ]
    ```

### 2.5. 样式与主题设计

  * **CSS变量体系：** 网站的核心样式(`style.css`)将使用CSS变量来定义颜色、字体等。
  * **主题文件：** 在`/src/css/themes/`目录下，为每个主题创建一个对应的CSS文件（如`theme-retro.css`）。每个文件仅定义一套完整的CSS变量值。
  * **动态加载：** 构建脚本会根据`config.json`中`selected_theme`的值，在每个生成的HTML页面的`<head>`中，自动引入对应的主题CSS文件。

### 2.6. 广告集成预留 (Ad Integration Reservation)

  * **潜在广告位：** 布局中需考虑在侧边栏、内容区块之间、列表内部等位置预留广告位。
  * **技术实现：** 布局采用CSS Flexbox或Grid，确保布局的灵活性。可在预留位置放置一个带有特定class的空`div`作为“广告占位符”。

## 3\. 页面模块与功能需求

### 3.1. 页头 (Header)

  * **Logo与网站名：** 链接至`/index.html`。
  * **导航栏:**
      * `HOME`: 链接到首页 (`/index.html`)。
      * `Popular Games`: 链接到一个新的列表页 (`/popular.html`)。
      * `New Games`: 链接到一个新的列表页 (`/new.html`)。
  * **搜索框：** 视觉占位符。

### 3.2. Iframe 游戏区域

  * **首页：** 加载由`games.json`第一个对象指定的特色游戏。
  * **游戏详情页：** 加载当前页对应的游戏。

### 3.3. Popular Games / New Games 区域

  * **出现范围：** 作为网站的标准侧边栏/内容模块，出现在**首页 (`index.html`)和所有游戏详情页**中。
  * **不出现范围：** 不会出现在分类列表页 (`popular.html` 和 `new.html`) 中。
  * **功能：** 列表内容由构建脚本根据`games.json`中除第一个对象以外的所有游戏动态生成。

### 3.4. Game Feature 区域

  * 此区域的内容由构建脚本遍历`games.json`中对应游戏的`feature_blocks`数组后动态生成。

### 3.5. FAQ 区域 & 3.6. 页脚 (Footer)

  * 内容相对固定，作为静态组件由构建脚本注入到每个页面。

## 4\. 页面规格

我们将网站页面明确定义为两种核心布局模板：

### 4.1. 核心布局页 (首页与游戏详情页)

  * **应用页面：** `index.html` 和所有游戏详情页 (如 `/popular_games/*.html`)。
  * **共享的完整结构：** Header, Iframe 游戏区域, Popular Games 区域, New Games 区域, Game Feature 区域, FAQ 区域, Footer。

### 4.2. 分类列表页 (`popular.html` 和 `new.html`)

  * **应用页面：** `popular.html` 和 `new.html`。
  * **页面结构：** 采用简洁的 `Header + Games区域 + Footer` 结构。
  * **Games区域:**
      * 包含一个页面主标题（例如`<h1>Popular Games</h1>`）。
      * 主标题下方是一个响应式的网格布局，用于**卡片化呈现**该分类下的所有游戏。
      * **视觉风格：** 整体布局和卡片风格应参考用户提供的视觉范例（`image_ad24e1.jpg`）。

## 5\. 开发、构建与部署流程

### 5.1. 源码与版本控制 (Git & GitHub)

  * 所有源代码托管于GitHub。`.gitignore`文件必须忽略`node_modules/`和`/dist/`目录。

### 5.2. 构建流程

通过在项目根目录执行`node build.js`命令来触发。脚本自动化执行以下任务：

1.  **清理：** 删除旧的`/dist`目录。
2.  **读取与分割数据：** 读取`games.json`，并将其分割为`homepageGameData` (`[0]`)和`listGamesData` (`[1:]`)。
3.  **验证主题配置：** 读取`config.json`，验证`selected_theme`的有效性，若无效则回退至默认主题。
4.  **生成页面：** 根据不同的页面规格和模板，结合对应的数据生成所有HTML文件。
5.  **拷贝静态资源：** 将`/src`目录下的CSS、JavaScript、images以及游戏源文件等静态资源复制到`/dist`目录。

### 5.3. 部署流程 (Cloudflare Pages)

  * 将Cloudflare Pages项目关联到GitHub仓库。
  * 配置构建命令为`node build.js`，输出目录为`dist`。
  * 代码推送到`main`分支后将自动触发构建和部署。

### 5.4. 日常维护流程 (更新游戏)

1.  **添加/修改文件：** （如果需要）将新的游戏源文件放入代码库。
2.  **编辑数据：** **打开并修改`games.json`文件**，这是最核心的步骤。
3.  **提交代码：** 将代码变更`commit`并`push`到GitHub的`main`分支。
4.  **完成：** Cloudflare Pages将自动完成后续所有部署工作。

## 6\. 最终交付物

### 6.1. 源码文件结构 (示例)

```
/project-gameport/
  ├── src/
  │   ├── components/
  │   ├── css/
  │   │   ├── themes/
  │   │   │   └── theme-default.css
  │   │   └── style.css
  │   ├── images/
  │   │   ├── popular_games_image/
  │   │   ├── new_games_image/
  │   │   └── common/
  │   └── templates/
  ├── .gitignore
  ├── build.js
  ├── config.json
  ├── games.json
  └── package.json
```

### 6.2. 交付成果文件结构 (部署内容)

```
/dist/
  ├── index.html
  ├── popular.html
  ├── new.html
  ├── popular_games/
  │   └── ...
  ├── new_games/
  │   └── ...
  ├── css/
  └── images/
      ├── popular_games_image/
      └── new_games_image/
      └── common/
```

## 7\. 非功能性需求

### 7.1. 搜索引擎优化 (SEO)

  * 所有页面必须是纯静态HTML。
  * 每个游戏详情页必须拥有其独立的、语义化的`<title>`和`<meta name="description">`标签。
  * 页面结构应使用`<h1>`, `<h2>`, `<header>`, `<main>`, `<nav>`等语义化标签。

### 7.2. 性能

  * 所有图片资源需进行压缩优化，优化首次页面加载时间（FCP）和最大内容绘制时间（LCP）。

### 7.3. 响应式设计

  * **核心技术：** 必须采用移动优先（Mobile-First）的开发策略。
  * **Viewport设置：** 所有HTML页面的`<head>`中必须包含`<meta name="viewport" content="width=device-width, initial-scale=1.0">`。
  * **布局技术：** 使用CSS Flexbox和Grid来实现灵活的、可自动换行的网格布局。
  * **媒体查询 (`@media`)：** 使用媒体查询来调整在不同屏幕尺寸下的布局、字体大小和元素间距。
  * **导航：** 在移动端，主导航栏应转换为汉堡菜单（Hamburger Menu）。

### 7.4. 浏览器兼容性

  * 需保证在主流现代浏览器的最新版本上正常工作，包括：Google Chrome, Mozilla Firefox, Apple Safari, Microsoft Edge。

-----

**文档结束**