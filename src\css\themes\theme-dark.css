/* GamePort - Dark Theme (暗夜游戏 - 深色系) */
/* 基于用户提供的色值设计的深色主题，营造沉浸式游戏氛围 */

:root {
  /* 主要颜色变量 - 深色系 */
  --primary-color: #6366f1;
  --primary-color-2: #6366f1;
  --primary-hover: #4f46e5;
  --primary-light: #e0e7ff;
  --secondary-color: #8b5cf6;
  --accent-color: #06b6d4;
  
  /* 背景颜色 - 基于用户提供的色值 */
  --bg-primary: #0C0D14;        /* Container背景 */
  --bg-secondary: #13141E;      /* Game Feature/FAQ背景 */
  --bg-tertiary: #212233;       /* Iframe Info背景 */
  --bg-info: #212233;  
  --bg-dark: #1F2030;          /* Header/Footer背景 */
  
  /* Header专用背景色 */
  --header-bg-color: #1F2030;   /* Header背景 */
  
  /* 文字颜色 - 适配深色背景 */
  --text-primary: #f8fafc;      /* 主要文字 - 浅色 */
  --text-secondary: #e2e8f0;    /* 次要文字 - 稍暗的浅色 */
  --text-light: #94a3b8;        /* 辅助文字 - 灰色 */
  --text-white: #ffffff;        /* 纯白文字 */
  
  /* 辅助颜色变量 */
  --border-color: #374151;      /* 边框颜色 - 深灰 */
  --border-hover: #4b5563;      /* 悬停边框 - 稍亮的灰 */
  --shadow-color: rgba(0, 0, 0, 0.3);     /* 阴影颜色 - 更深 */
  --shadow-hover: rgba(0, 0, 0, 0.5);     /* 悬停阴影 - 更深 */
  
  /* 状态颜色 */
  --success-color: #10b981;     /* 成功 - 绿色 */
  --warning-color: #f59e0b;     /* 警告 - 橙色 */
  --error-color: #ef4444;       /* 错误 - 红色 */
  --info-color: #06b6d4;        /* 信息 - 青色 */
  
  /* 按钮颜色状态 */
  --btn-primary-bg: #6366f1;
  --btn-primary-hover: #4f46e5;
  --btn-primary-active: #3730a3;
  --btn-secondary-bg: #8b5cf6;
  --btn-secondary-hover: #7c3aed;
  
  /* 链接颜色 */
  --link-color: #6366f1;
  --link-hover: #4f46e5;
  --link-visited: #8b5cf6;
  
  /* 特殊效果颜色 */
  --gradient-primary: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  --gradient-secondary: linear-gradient(135deg, #1F2030 0%, #13141E 100%);
  
  /* 游戏相关特殊背景 */
  --game-iframe-bg: rgba(33, 34, 51, 0.8);    /* Iframe背景 - 带透明度 */
  --game-iframe-alt-bg: #2E292E;              /* Iframe备选背景 */
  --game-info-bg: #212233;                    /* 游戏信息栏背景 */
  --game-feature-bg: #13141E;                 /* 游戏特性区域背景 */
  --faq-bg: #13141E;                          /* FAQ区域背景 */
  
  /* 卡片和容器背景 */
  --card-bg: #1a1b26;          /* 卡片背景 - 比主背景稍亮 */
  --card-hover-bg: #21222d;    /* 卡片悬停背景 */
  
  /* 输入框和表单元素 */
  --input-bg: #1a1b26;         /* 输入框背景 */
  --input-border: #374151;     /* 输入框边框 */
  --input-focus-border: #6366f1; /* 输入框聚焦边框 */
  
  /* 滚动条样式 */
  --scrollbar-track: #1a1b26;  /* 滚动条轨道 */
  --scrollbar-thumb: #374151;  /* 滚动条滑块 */
  --scrollbar-thumb-hover: #4b5563; /* 滚动条滑块悬停 */
}

/* 深色主题特殊样式覆盖 */

/* 游戏容器特殊背景 */
.game-container {
  background: var(--game-iframe-bg) !important;
  border-color: var(--border-color) !important;
}

/* 游戏信息栏 */
.game-info-bar {
  background: var(--game-info-bg) !important;
  border-color: var(--border-color) !important;
  color: var(--text-primary) !important;
}

.game-info-title {
  color: var(--text-primary) !important;
}

/* 游戏特性区域 */
.game-feature-section {
  background-color: var(--game-feature-bg) !important;
  border-color: var(--border-color) !important;
}

/* FAQ区域 */
.faq-section {
  background-color: var(--faq-bg) !important;
  border-color: var(--border-color) !important;
}

/* 卡片样式增强 */
.card,
.game-card {
  background-color: var(--card-bg) !important;
  border-color: var(--border-color) !important;
}

.card:hover,
.game-card:hover {
  background-color: var(--card-hover-bg) !important;
  border-color: var(--border-hover) !important;
}

/* 搜索框样式 */
.search-box {
  background-color: var(--input-bg) !important;
  border-color: var(--input-border) !important;
}

.search-input {
  background: var(--input-bg) !important;
  color: var(--text-primary) !important;
  border-color: var(--input-border) !important;
}

.search-input:focus {
  border-color: var(--input-focus-border) !important;
}

/* 搜索结果 */
.search-results {
  background: var(--card-bg) !important;
  border-color: var(--border-color) !important;
}

.search-result-item:hover {
  background: var(--card-hover-bg) !important;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--scrollbar-track);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: var(--scrollbar-thumb);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--scrollbar-thumb-hover);
}

/* Firefox滚动条 */
* {
  scrollbar-width: thin;
  scrollbar-color: var(--scrollbar-thumb) var(--scrollbar-track);
}

/* 页面头部特殊处理 */
.page-header {
  background: var(--gradient-secondary) !important;
  border-color: var(--border-color) !important;
}

/* 游戏预览区域 */
.game-preview {
  background: var(--bg-secondary) !important;
}

/* 按钮增强效果 */
.btn-primary {
  background: var(--gradient-primary) !important;
  border: none !important;
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3) !important;
}

.btn-primary:hover {
  box-shadow: 0 6px 16px rgba(99, 102, 241, 0.4) !important;
  transform: translateY(-1px) !important;
}

/* 全屏按钮 */
.fullscreen-btn {
  background: var(--primary-color) !important;
  color: var(--text-white) !important;
}

.fullscreen-btn:hover {
  background: var(--primary-hover) !important;
}

/* 播放按钮 */
.play-btn {
  background: var(--gradient-primary) !important;
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3) !important;
}

.play-btn:hover {
  box-shadow: 0 6px 16px rgba(99, 102, 241, 0.4) !important;
}

/* 导航链接活跃状态 */
.nav-link.active::after {
  background: var(--primary-color) !important;
}

/* 面包屑导航 */
.breadcrumb-nav {
  border-color: var(--border-color) !important;
}

/* 页脚链接 */
.footer-link:hover {
  color: var(--primary-color) !important;
}

/* 加载动画 */
.loading-spinner {
  border-color: var(--border-color) !important;
  border-top-color: var(--primary-color) !important;
}

/* 错误状态 */
.game-error {
  background: var(--bg-secondary) !important;
}

/* 特殊区域边框增强 */
.game-feature-section,
.faq-section {
  border-top: none !important;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .search-input {
    background: var(--input-bg) !important;
  }
  
  .nav-menu.active {
    background: var(--bg-dark) !important;
    border: 1px solid var(--border-color) !important;
    border-radius: 8px !important;
    margin-top: 8px !important;
  }
}
