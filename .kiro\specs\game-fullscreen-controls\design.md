# Design Document

## Overview

Based on the reference implementation, the game info bar should be positioned outside the game container and always visible. The structure should be:

1. Game container (with preview/iframe switching)
2. Game info bar (always visible, independent)

## Architecture

### HTML Structure
```html
<div class="game-section">
    <div class="game-container">
        <div class="game-preview" id="gamePreview">
            <!-- Game preview content -->
        </div>
        <div class="game-iframe" id="gameIframe" style="display: none;">
            <!-- Game iframe -->
        </div>
    </div>
    
    <!-- Game info bar - always visible -->
    <div class="game-info-bar">
        <div class="game-info-left">
            <img src="..." class="game-info-thumbnail" width="40" height="40">
        </div>
        <div class="game-info-center">
            <h2>Game Title</h2>
        </div>
        <div class="game-info-right">
            <button class="fullscreen-btn" onclick="toggleFullscreen()">
                <!-- Fullscreen icon -->
            </button>
        </div>
    </div>
</div>
```

## Components and Interfaces

### Game Preview Component
- Displays game thumbnail and play button
- Switches to iframe when play button is clicked

### Game Info Bar Component
- Always visible below game area
- Contains: 40x40 thumbnail, game title, fullscreen button
- Independent of game state (preview/playing)

### Fullscreen Functionality
- Targets the iframe element directly
- Uses browser's native fullscreen API

## Data Models

- Game thumbnail URL
- Game title
- Game iframe URL

## Error Handling

- Check if iframe exists before attempting fullscreen
- Fallback for browsers without fullscreen support

## Testing Strategy

- Test info bar visibility in both preview and game states
- Test fullscreen functionality across browsers
- Test responsive behavior on mobile devices