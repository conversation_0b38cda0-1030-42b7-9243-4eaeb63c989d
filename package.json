{"name": "gameport-static-site", "version": "1.0.0", "description": "静态游戏门户网站 - 高性能、SEO友好的游戏平台", "main": "build.js", "scripts": {"build": "node build.js", "dev": "node build.js && echo 'Build completed! Open dist/index.html to view the site.'", "clean": "rmdir /s /q dist 2>nul || echo 'Dist folder cleaned or not found'", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["games", "static-site", "ssg", "html5", "css3", "javascript", "responsive", "seo"], "author": "GamePort Team", "license": "MIT", "engines": {"node": ">=14.0.0"}, "devDependencies": {}, "dependencies": {}}