# 🚀 GamePort 构建脚本计划

## 📋 构建脚本需要生成的文件

### 1. 主页 (index.html)
- **模板**: `src/templates/_main-layout-template.html`
- **内容**: 
  - `{{POPULAR_GAMES_LIST}}` → 从games.json筛选category="popular"的游戏，生成游戏卡片
  - `{{NEW_GAMES_LIST}}` → 从games.json筛选category="new"的游戏，生成游戏卡片
- **游戏卡片**: 使用`src/components/game-card.html`模板
- **链接**: 每个游戏卡片链接到对应的游戏详情页 (如 `super-mario-bros.html`)

### 2. 游戏详情页 (每个游戏一个HTML文件)
- **模板**: `src/templates/game-detail-template.html`
- **文件名**: `{game-id}.html` (如 `super-mario-bros.html`, `pac-man.html`)
- **内容**:
  - `{{GAME_NAME}}` → 游戏名称
  - `{{GAME_THUMBNAIL}}` → 游戏缩略图
  - `{{GAME_URL}}` → 实际游戏链接 (gameUrl字段)
  - `{{GAME_FEATURE_BLOCKS}}` → 渲染feature_blocks内容
  - `{{GAME_META_TITLE}}` → SEO标题
  - `{{GAME_META_DESCRIPTION}}` → SEO描述
  - `{{RELATED_GAMES_LIST}}` → 相关游戏推荐

### 3. 列表页面
- **popular.html**: 所有popular游戏的列表页
- **new.html**: 所有new游戏的列表页
- **模板**: `src/templates/_list-layout-template.html`

## 🎮 Feature Blocks 渲染规则

### YouTube视频块
```json
{
  "type": "youtube",
  "videoId": "rLl9XBg7wSs",
  "title": "Super Mario Bros Gameplay"
}
```
**渲染为**: YouTube嵌入iframe

### 内容块 (What is / How to Play)
```json
{
  "type": "section",
  "title": "What is Super Mario Bros",
  "content": "详细内容..."
}
```
**渲染为**: 带标题的内容块

## 🔧 构建脚本功能

### 主要功能
1. **读取games.json数据**
2. **生成游戏卡片HTML** (用于主页和列表页)
3. **生成feature blocks HTML** (用于详情页)
4. **替换模板占位符**
5. **输出最终HTML文件**

### 输出文件结构
```
dist/
├── index.html                 # 主页
├── popular.html              # Popular游戏列表
├── new.html                  # New游戏列表
├── super-mario-bros.html     # 游戏详情页
├── pac-man.html              # 游戏详情页
├── tetris.html               # 游戏详情页
├── space-invaders.html       # 游戏详情页
├── frogger.html              # 游戏详情页
├── snake.html                # 游戏详情页
├── css/                      # 复制CSS文件
├── js/                       # 复制JS文件
└── images/                   # 复制图片文件
```

## 🎯 关键点

1. **主页游戏卡片** → 链接到游戏详情页
2. **详情页Play Now按钮** → 链接到实际游戏URL
3. **Feature blocks** → 只在详情页显示
4. **响应式设计** → 所有页面都支持移动端
5. **SEO优化** → 每个页面都有独特的meta标签
